#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cập nhật địa chỉ brand_office dựa trên geometry data
Task: Sử dụng dữ liệu geometry để xác định chính xác xã/phường và tỉnh/thành phố
"""

import json
import logging
import pandas as pd
import geopandas as gpd
import mysql.connector
from mysql.connector import Error
from shapely.geometry import Point, shape
import warnings
import time

from gemini import gemini
from gemini_optimized import MemoryOptimizedGemini
warnings.filterwarnings('ignore')
from tqdm.asyncio import tqdm
import asyncio


# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exports/update_address.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BrandOfficeAddressUpdater:
    def __init__(self, memory_limit_mb=1024):
        self.connection = None
        self.results = []
        self.batch_size = 1000
        self.processed_count = 0
        self.matched_count = 0
        self.unmatched_count = 0
        self.gemini = gemini
        self.memory_optimizer = MemoryOptimizedGemini(
            memory_limit_mb=memory_limit_mb
        )
        logger.info(f"🧠 Memory optimization enabled (separate instance, limit: {memory_limit_mb}MB)")
    

    def check_dataset_size_and_optimize(self):
        """Kiểm tra dataset size và auto-enable memory optimization nếu cần"""
        try:
            # Count total records
            query = """
            SELECT COUNT(*) as total_count
            FROM brand_office
            WHERE address_old IS NOT NULL
            AND address_old != ''
            AND latitude IS NOT NULL
            AND longitude IS NOT NULL
            and status = 2
            """

            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query)
            result = cursor.fetchone()
            cursor.close()

            total_count = result['total_count'] if result else 0
            logger.info(f"📊 Total dataset size: {total_count:,} records")
        

            return total_count

        except Exception as e:
            logger.warning(f"⚠️ Could not check dataset size: {e}")
            return 0

    async def process_with_memory_optimization(self, data_stream, geo_ward_data):
        """Xử lý data với memory optimization (nhận vào một stream)"""
        if not self.memory_optimizer:
            logger.error("❌ Memory optimizer not initialized")
            return []

        logger.info("🧠 Processing với memory optimization (separate Gemini instance)")

        # Truyền thẳng data_stream vào memory_optimizer
        results = await self.memory_optimizer.process_large_dataset(
            data_stream, geo_ward_data,
            output_file='exports/memory_optimized_results.csv'
        )
        return results

    def get_database_connection(self):
        """Tạo kết nối database"""
        try:
            connection = mysql.connector.connect(
                host='127.0.0.1',
                port=3306,
                database='urbox',
                user='root',
                password='root',
                charset='utf8mb4'
            )
            
            if connection.is_connected():
                logger.info("✅ Kết nối database thành công!")
                return connection
                
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return None
    
    def get_brand_office_data(self, offset=0, limit=2000):
        """Lấy dữ liệu brand_office theo batch"""
        query = """
        SELECT id, latitude, longitude, city_id, address_old
        FROM brand_office
        WHERE address_old IS NOT NULL
        AND address_old != ''
        AND latitude IS NOT NULL
        AND longitude IS NOT NULL
        LIMIT %s OFFSET %s
        """
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query, (limit, offset))
            results = cursor.fetchall()
            cursor.close()
            
            logger.info(f"📊 Lấy được {len(results)} records brand_office (offset: {offset})")
            return results
            
        except Error as e:
            logger.error(f"❌ Lỗi lấy dữ liệu brand_office: {e}")
            return []

    async def stream_brand_office_data(self):
        """
        Tạo một generator để stream dữ liệu từ database thay vì tải tất cả.
        """
        offset = 0
        while True:
            batch_data = self.get_brand_office_data(offset, self.batch_size)
            if not batch_data:
                break

            logger.info(f"Streamed {len(batch_data)} records from database (offset: {offset})")
            yield batch_data

            await asyncio.sleep(0.01) # Small sleep to prevent blocking
            offset += self.batch_size

    def get_geo_ward_data(self):
        """Lấy dữ liệu geo_ward và trả về GeoDataFrame với spatial index"""
        load_start_time = time.time()

        query = """
        SELECT geometry, geo_province_code, province_title, ward_title, code
        FROM geo_ward
        WHERE geometry IS NOT NULL
        """

        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute(query)
            results = cursor.fetchall()
            cursor.close()

            logger.info(f"📊 Lấy được {len(results)} geo_ward records")

            # Chuyển đổi sang GeoDataFrame
            gdf_data = []
            for row in results:
                try:
                    # Parse geometry từ JSON string
                    if isinstance(row['geometry'], str):
                        geometry_data = json.loads(row['geometry'])
                    else:
                        geometry_data = row['geometry']

                    geometry = shape(geometry_data)

                    gdf_data.append({
                        'geometry': geometry,
                        'geo_province_code': row['geo_province_code'],
                        'province_title': row['province_title'],
                        'ward_title': row['ward_title'],
                        'code': row['code']
                    })
                except Exception as e:
                    logger.warning(f"⚠️ Lỗi parse geometry cho ward {row.get('code', 'unknown')}: {e}")
                    continue

            if not gdf_data:
                logger.error("❌ Không có geometry hợp lệ nào")
                return gpd.GeoDataFrame()

            # Tạo GeoDataFrame
            gdf = gpd.GeoDataFrame(gdf_data, crs='EPSG:4326')

            # Tạo spatial index để tối ưu hóa tìm kiếm
            logger.info("🔍 Tạo spatial index cho GeoDataFrame...")
            spatial_index_start = time.time()
            gdf.sindex  # Trigger spatial index creation
            spatial_index_time = (time.time() - spatial_index_start) * 1000

            total_load_time = (time.time() - load_start_time) * 1000



            logger.info(f"✅ Đã tạo GeoDataFrame với {len(gdf)} records và spatial index")
            return gdf

        except Error as e:
            logger.error(f"❌ Lỗi lấy geo_ward data: {e}")
            return gpd.GeoDataFrame()
    
    def parse_geometry(self, geometry_str):
        """Parse geometry từ JSON string thành Shapely object"""
        try:
            if isinstance(geometry_str, str):
                geometry_data = json.loads(geometry_str)
            else:
                geometry_data = geometry_str
                
            return shape(geometry_data)
        except Exception as e:
            logger.warning(f"⚠️ Lỗi parse geometry: {e}")
            return None
    
    def find_ward_by_coordinates(self, lat, lng,  geo_ward_gdf):
        """Tìm ward chứa tọa độ sử dụng spatial index"""
        query_start_time = time.time()

        try:
            # Kiểm tra nếu geo_ward_gdf là GeoDataFrame
            if isinstance(geo_ward_gdf, gpd.GeoDataFrame):
                result = self.find_ward_by_lat_lng(lat, lng, geo_ward_gdf)

                ward, match_type = result
               
                return result
            else:
                result = self.find_ward_by_lat_lng_legacy(lat, lng, geo_ward_gdf)

                # Track legacy query
                ward, match_type = result
               
                return result

        except Exception as e:
            logger.warning(f"⚠️ Lỗi tìm ward cho tọa độ ({lat}, {lng}): {e}")
           
            return None, 'error'

    def find_ward_by_lat_lng(self, lat, lng, geo_ward_gdf):
        """Tìm ward chứa tọa độ sử dụng spatial index (tối ưu hóa)"""
        query_start_time = time.time()
        candidates_count = None
        method = 'unknown'

        try:
            point = Point(lng, lat)

            # Kiểm tra nếu là GeoDataFrame
            if isinstance(geo_ward_gdf, gpd.GeoDataFrame):
                method = 'spatial_index'
                if len(geo_ward_gdf) == 0:
                   
                    return None, 'no_data'

                logger.debug(f"🔍 Tìm kiếm spatial index trong {len(geo_ward_gdf)} wards cho tọa độ ({lat}, {lng})")

                # Sử dụng spatial index để tìm candidates nhanh chóng
                try:
                    # Lấy possible matches từ spatial index
                    possible_matches_idx = list(geo_ward_gdf.sindex.intersection(point.bounds))
                    candidates_count = len(possible_matches_idx)

                    if not possible_matches_idx:
                        logger.debug(f"🔍 Không tìm thấy candidates từ spatial index")
                       
                        return None, 'no_match'

                    logger.debug(f"🔍 Spatial index tìm được {len(possible_matches_idx)} candidates")

                    # Kiểm tra chính xác các candidates
                    for idx in possible_matches_idx:
                        try:
                            ward_row = geo_ward_gdf.iloc[idx]
                            geometry = ward_row.geometry

                            if geometry:
                                # Thử contains() trước (chính xác hơn)
                                if geometry.contains(point):
                                    ward_dict = ward_row.to_dict()
                                   
                                    return ward_dict, 'contains'
                                # Fallback: intersects() (cho trường hợp point ở biên)
                                elif geometry.intersects(point):
                                    ward_dict = ward_row.to_dict()
                                   
                                    return ward_dict, 'intersects'
                        except Exception as candidate_error:
                            logger.warning(f"⚠️ Lỗi xử lý candidate {idx}: {candidate_error}")
                            continue

                    # Fallback: buffer search cho các candidates
                    for idx in possible_matches_idx:
                        try:
                            ward_row = geo_ward_gdf.iloc[idx]
                            geometry = ward_row.geometry

                            if geometry and geometry.buffer(0.001).intersects(point):
                                ward_dict = ward_row.to_dict()
                               
                                return ward_dict, 'buffer'
                        except Exception as buffer_error:
                            logger.warning(f"⚠️ Lỗi buffer search candidate {idx}: {buffer_error}")
                            continue

                   
                    return None, 'no_match'

                except Exception as spatial_error:
                    logger.warning(f"⚠️ Lỗi spatial index, fallback to linear search: {spatial_error}")
                    # Fallback to linear search nếu spatial index lỗi
                    method = 'linear_search'
                    try:
                        # Convert GeoDataFrame to records safely
                        geo_ward_records = geo_ward_gdf.to_dict('records')
                        result = self.find_ward_by_lat_lng_legacy(lat, lng, geo_ward_records)
                        ward, match_type = result
                       
                        return result
                    except Exception as fallback_error:
                        logger.error(f"❌ Lỗi fallback search: {fallback_error}")
                       
                        return None, 'error'
            else:
                # Backward compatibility: nếu vẫn là list
                method = 'linear_search'
                result = self.find_ward_by_lat_lng_legacy(lat, lng, geo_ward_gdf)
                ward, match_type = result
               
                return result

        except Exception as e:
            logger.warning(f"⚠️ Lỗi tìm ward tại ({lat}, {lng}): {e}")
            return None, 'error'

    def find_ward_by_lat_lng_legacy(self, lat, lng, geo_ward_data):
        """Hàm tìm kiếm legacy (linear search) để backward compatibility"""
        try:
            point = Point(lng, lat)

            logger.debug(f"🔍 Linear search trong {len(geo_ward_data)} wards cho tọa độ ({lat}, {lng})")

            for ward in geo_ward_data:
                try:
                    geometry = None

                    if isinstance(ward, dict):
                        geometry = self.parse_geometry(ward['geometry'])
                    elif hasattr(ward, 'geometry'):
                        # Pandas Series hoặc object có geometry attribute
                        geometry = ward.geometry
                    elif hasattr(ward, 'get') and callable(ward.get):
                        # Object có method get()
                        geometry = self.parse_geometry(ward.get('geometry'))
                    else:
                        # Skip nếu không thể xử lý
                        logger.debug(f"⚠️ Không thể xử lý ward type: {type(ward)}")
                        continue

                    if geometry:
                        # Thử contains() trước (chính xác hơn)
                        if geometry.contains(point):
                            return ward, 'contains'
                        # Fallback: intersects() (cho trường hợp point ở biên)
                        elif geometry.intersects(point):
                            return ward, 'intersects'
                        # Fallback: buffer cho trường hợp point ở gần đó
                        elif geometry.buffer(0.001).intersects(point):
                            return ward, 'buffer'

                except Exception as ward_error:
                    logger.debug(f"⚠️ Lỗi xử lý ward: {ward_error}")
                    continue
            return None, 'no_match'

        except Exception as e:
            logger.warning(f"⚠️ Lỗi legacy search tại ({lat}, {lng}): {e}")
            return None, 'error'


    async def process_batch(self, brand_office_data, geo_ward_data, initial_prompt):
        """Xử lý một batch dữ liệu"""
        batch_results = []
        tasks = []
        for record in tqdm(brand_office_data, total=len(brand_office_data), desc="Đang xử lý địa chỉ"):
            try:               
                # Tìm ward chứa tọa độ
                ward, _ = self.find_ward_by_coordinates(
                    record['latitude'], record['longitude'], geo_ward_data
                )
                if ward:
                    # Tạo task xử lý bất đồng bộ
                    tasks.append(self._process_record_async(record, ward, initial_prompt))
                else:
                    # Không tìm được ward
                    result = {
                        'id': record['id'],
                        'latitude': record['latitude'],
                        'longitude': record['longitude'],
                        'city_id': record['city_id'],
                        'geo_province_code': None,
                        'geo_ward_code': None,
                        'address_old': record['address_old'],
                        'ward_code': None,
                        'ward_title': None,
                        'province_title': None,
                        'new_address': None,
                        'status': 'unmatched'
                    }
                    batch_results.append(result)
                    self.unmatched_count += 1
                self.processed_count += 1
                if self.processed_count % 100 == 0:
                    logger.info(f"📊 Đã xử lý {self.processed_count} records")
            except Exception as e:
                logger.error(f"❌ Lỗi xử lý record ID {record['id']}: {e}")
                continue
        # Chờ tất cả các task async hoàn thành
        if tasks:
            batch_results.extend(await asyncio.gather(*tasks))
        return batch_results


    async def _process_record_async(self, record, ward, initial_prompt):
        try:
            # Validate ward data
            if not isinstance(ward, dict):
                logger.error(f"❌ Ward data không phải dictionary: {type(ward)} - {ward}")
                raise ValueError(f"Ward data must be dictionary, got {type(ward)}")

            # Validate required fields
            required_fields = ['ward_title', 'province_title', 'geo_province_code', 'code']
            for field in required_fields:
                if field not in ward:
                    logger.error(f"❌ Ward thiếu field '{field}': {ward}")
                    raise ValueError(f"Ward missing required field: {field}")

            new_address = await self.gemini.convert_address_async(initial_prompt, record, ward['ward_title'], ward['province_title'])
            result = {
                'id': record['id'],
                'latitude': record['latitude'],
                'longitude': record['longitude'],
                'city_id': record['city_id'],
                'geo_province_code': ward['geo_province_code'],
                'geo_ward_code': ward['code'],
                'address_old': record['address_old'],
                'ward_code': ward['code'],
                'ward_title': ward['ward_title'],
                'province_title': ward['province_title'],
                'new_address': new_address,
                'status': 'matched'
            }
            self.matched_count += 1
            return result
        except Exception as e:
            logger.error(f"❌ Lỗi xử lý record ID {record['id']}: {e}")
            logger.error(f"   Ward data: {ward}")
            logger.error(f"   Ward type: {type(ward)}")
            return {
                'id': record['id'],
                'latitude': record['latitude'],
                'longitude': record['longitude'],
                'city_id': record['city_id'],
                'geo_province_code': ward['geo_province_code'],
                'geo_ward_code': ward['code'],
                'address_old': record['address_old'],
                'ward_code': ward['code'],
                'ward_title': ward['ward_title'],
                'province_title': ward['province_title'],
                'new_address': None,
                'status': 'error'
            }

    def save_results_to_csv(self, results, filename='brand_office_updated.csv'):
        """Lưu kết quả ra CSV"""
        try:
            df = pd.DataFrame(results)
            filepath = f"exports/{filename}"
            df.to_csv(filepath, index=False, encoding='utf-8')
            
            logger.info(f"✅ Đã lưu {len(results)} records vào {filepath}")
            
            # Thống kê
            matched = len([r for r in results if r['status'] == 'matched'])
            unmatched = len([r for r in results if r['status'] == 'unmatched'])
            
            logger.info(f"📊 THỐNG KÊ: Matched: {matched}, Unmatched: {unmatched}")
            
        except Exception as e:
            logger.error(f"❌ Lỗi lưu CSV: {e}")

    async def run(self):
        """Chạy toàn bộ process với async support"""
        try:
            logger.info("🚀 BẮT ĐẦU CẬP NHẬT ĐỊA CHỈ BRAND_OFFICE")

            # Kết nối database
            self.connection = self.get_database_connection()
            if not self.connection:
                return

            # Check dataset size và auto-optimize
            total_records = self.check_dataset_size_and_optimize()

            # Lấy dữ liệu mapping và geo_ward
            logger.info("📊 Lấy dữ liệu mapping và geometry...")
            geo_ward_data = self.get_geo_ward_data()

            if geo_ward_data.empty:
                logger.error("❌ Không có dữ liệu geo_ward")
                return

            logger.info("" + "="*60)
            logger.info("🔄 XỬ LÝ RECORDS")
            logger.info("="*60)
            logger.info("🧠 Using memory optimization for processing")
            data_stream = self.stream_brand_office_data()
            memory_results = await self.process_with_memory_optimization(
                data_stream, geo_ward_data
            )
            logger.info(f"✅ MEMORY OPTIMIZATION COMPLETED! Processed: {memory_results.get('total_processed', 0)}")
        except Exception as e:
            logger.error(f"❌ Lỗi trong quá trình xử lý: {e}")
        finally:
            if self.connection:
                self.connection.close()

if __name__ == "__main__":
    import asyncio
    updater = BrandOfficeAddressUpdater()
    asyncio.run(updater.run())
