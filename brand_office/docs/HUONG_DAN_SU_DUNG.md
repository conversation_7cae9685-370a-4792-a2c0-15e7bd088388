# Hướng dẫn Sử dụng - Brand Office Address Update Tool

Tài liệu này cung cấp hướng dẫn toàn diện để cài đặt, cấu hình và vận hành công cụ cập nhật địa chỉ Brand Office.

## 1. C<PERSON>i đặt Môi trường

Trước khi bắt đầu, hãy đảm bảo bạn đã cài đặt đầy đủ các thư viện cần thiết.

### Yêu cầu:
- Python 3.8+
- MySQL Server đang hoạt động

### Các bước cài đặt:

**Cách 1: C<PERSON>i đặt thủ công**
```bash
pip install -r brand_office/requirements.txt
```
*Lưu ý: Nếu gặp lỗi với `geopandas`, bạn có thể cần cài đặt các thư viện hệ thống như `gdal` trước.*

**Cách 2: S<PERSON> dụng <PERSON> (Khuyến nghị)**
<PERSON><PERSON>ng cụ sẽ tự động kiểm tra và cài đặt gi<PERSON><PERSON> bạn.
```bash
python brand_office/brand_office_toolkit.py
# Chọn Option 1: "Kiểm tra và cài đặt requirements"
```

## 2. Cấu hình

### Kết nối Database
Công cụ được cấu hình để kết nối với MySQL theo thông tin sau:
- **Host**: `127.0.0.1`
- **Port**: `3306`
- **User**: `root`
- **Password**: `root`
- **Database**: `urbox`

Hãy đảm bảo database của bạn đang hoạt động và thông tin kết nối là chính xác. Bạn có thể thay đổi các thông số này trực tiếp trong file `update_brand_office_address.py` nếu cần.

### API Key (Cho xử lý bằng AI)
Để sử dụng tính năng xử lý địa chỉ bằng Gemini AI, bạn cần cung cấp API Key.
- **Mở file**: `brand_office/gemini.py`
- **Tìm và thay thế**:
  ```python
  genai.configure(api_key='YOUR_GEMINI_API_KEY')
  ```

## 3. Vận hành Công cụ

Chúng tôi khuyến nghị sử dụng **Interactive Toolkit** để có trải nghiệm tốt nhất.

### Sử dụng Interactive Toolkit
Chạy lệnh sau từ thư mục gốc của dự án:
```bash
python brand_office/brand_office_toolkit.py
```

**Menu chính:**
```
============================================================
🏢 BRAND OFFICE ADDRESS UPDATE TOOLKIT
============================================================
1. Kiểm tra và cài đặt requirements
2. Thiết lập thư mục
3. Chạy cập nhật địa chỉ (auto-detect memory)
4. Chạy với memory optimization (large datasets)
5. Xem kết quả
6. Phân tích records không match
7. Xem kết quả city_id=0 improvement
8. Chạy toàn bộ (1+2+3)
0. Thoát
============================================================
```

**Workflow khuyến nghị:**
1.  **Chọn Option 1** để đảm bảo môi trường đã sẵn sàng.
2.  **Chọn Option 8** để thực hiện toàn bộ quy trình cập nhật một cách tự động. Tool sẽ:
    -   Tạo các thư mục cần thiết.
    -   Tự động phát hiện kích thước dữ liệu để bật chế độ tối ưu bộ nhớ nếu cần.
    -   Chạy cập nhật cho cả bản ghi thường và bản ghi `city_id=0`.
    -   Hiển thị thống kê kết quả sau khi hoàn thành.
3.  **Chọn các Option 5, 6, 7** để xem và phân tích kết quả chi tiết.

### Chạy trực tiếp (Dành cho người dùng nâng cao)
Bạn cũng có thể chạy các script xử lý một cách độc lập.
```bash
# Chạy chế độ xử lý chính (tích hợp AI, tự động tối ưu bộ nhớ)
python brand_office/update_brand_office_address.py

# Chạy chế độ xử lý với Gemini AI và tùy chỉnh rate limit
python brand_office/update_with_gemini.py --limit 1000 --rate-limit 1500

# Chạy chế độ tối ưu bộ nhớ cho dữ liệu cực lớn
python brand_office/gemini_optimized.py
```

## 4. Diễn giải Kết quả

Kết quả xử lý sẽ được lưu trong thư mục `brand_office/exports/`.

### Các file output chính:
-   `brand_office_updated.csv`: Kết quả xử lý cho các bản ghi thông thường (`city_id > 0`).
-   `city_id_zero_improved_YYYYMMDD_HHMMSS.csv`: Kết quả xử lý cho các bản ghi đặc biệt (`city_id = 0`).
-   `unmatched_records.csv`: Danh sách các bản ghi không tìm thấy thông tin xã/phường phù hợp.
-   `update_address.log`: Log chi tiết toàn bộ quá trình xử lý.

### Cấu trúc file CSV kết quả:
| Cột | Mô tả |
| :--- | :--- |
| `id` | ID của brand_office. |
| `latitude`, `longitude` | Tọa độ GPS. |
| `address_old` | Địa chỉ gốc. |
| `ward_title` | Tên Xã/Phường mới tìm được. |
| `province_title` | Tên Tỉnh/Thành phố mới tìm được. |
| `new_address` | **Địa chỉ mới đã được chuẩn hóa.** |
| `status` | Trạng thái xử lý: `matched`, `unmatched`, `error`. |
| `match_type` | (Chỉ có ở file city\_id=0) Phương pháp match: `contains`, `intersects`, `buffer`. |

### Phân tích Thống kê
Toolkit sẽ tự động hiển thị các thông tin quan trọng sau khi chạy xong:
-   **Tỷ lệ thành công (Success Rate):** Phần trăm bản ghi được cập nhật thành công.
-   **Phân loại Match Type:** Giúp hiểu rõ mức độ chính xác của các kết quả `city_id=0`.
-   **Tốc độ xử lý (RPM):** Số lượng bản ghi được xử lý mỗi phút (khi dùng AI).
-   **Thời gian xử lý và Bộ nhớ sử dụng.**

## 5. Xử lý Lỗi (Troubleshooting)

| Vấn đề | Nguyên nhân | Giải pháp |
| :--- | :--- | :--- |
| **Lỗi kết nối Database** | MySQL chưa chạy, sai thông tin kết nối. | - Đảm bảo dịch vụ MySQL đang hoạt động.<br>- Kiểm tra lại cấu hình trong code. |
| **Lỗi hết bộ nhớ (Memory Error)** | Dataset quá lớn. | - Chạy bằng **Option 4** của toolkit.<br>- Tool sẽ tự động bật chế độ tối ưu nếu phát hiện dữ liệu lớn. |
| **Rate Limit Reached** | Vượt quá giới hạn request của Gemini API. | - Đây là hành vi bình thường, tool sẽ **tự động chờ** và tiếp tục.<br>- Nếu xảy ra liên tục, hãy giảm `--rate-limit` khi chạy script thủ công. |
| **Nhiều bản ghi `unmatched`** | Tọa độ nằm ngoài vùng geometry, dữ liệu geometry không đầy đủ. | - Mở file `unmatched_records.csv` để kiểm tra.<br>- Cân nhắc cập nhật lại dữ liệu `geo_ward` trong database. |
