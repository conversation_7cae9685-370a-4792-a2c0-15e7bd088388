# Workflow Xử lý - Brand Office Address Update Tool

Tài liệu này mô tả chi tiết luồng xử lý dữ liệu của công cụ, từ đầu vào đến đầu ra, bao gồm cả các quy trình xử lý đặc biệt.

## S<PERSON> đồ Luồng dữ liệu Tổng quan

```mermaid
graph TD
    A[Bắt đầu] --> B{Lấy dữ liệu Brand Office};
    B --> C{Phân loại bản ghi};
    C --> D[city_id > 0];
    C --> E[city_id = 0];

    D --> F[Workflow Xử lý Thông thường];
    E --> G[Workflow Xử lý Đặc biệt];

    F --> H{Geometry Matching};
    G --> H;

    H --> I{Chuẩn hóa địa chỉ bằng AI};
    I --> J[Lưu kết quả vào CSV];
    J --> <PERSON>[<PERSON><PERSON><PERSON> thúc];

    subgraph "Input Data"
        DB[(Database MySQL)] -- "brand_office, geo_ward, __province" --> B;
    end

    subgraph "Output Data"
        J -- "brand_office_updated.csv" --> L[File CSV Thường];
        J -- "city_id_zero_improved.csv" --> M[File CSV city_id=0];
    end
```

--- 

## Giai đoạn 1: Chuẩn bị và Phân loại

1.  **Kết nối Database**: Tool kết nối đến MySQL và chuẩn bị các kết nối cần thiết.
2.  **Tải dữ liệu tham chiếu**: Tải toàn bộ dữ liệu từ bảng `geo_ward` (chứa polygons) và `__province` (chứa mapping tỉnh thành) vào bộ nhớ để tăng tốc độ truy vấn.
3.  **Lấy và Phân loại Dữ liệu Gốc**:
    -   Tool truy vấn bảng `brand_office` để lấy các bản ghi cần xử lý.
    -   Các bản ghi được chia thành hai luồng chính:
        -   **Luồng Thông thường**: Các bản ghi có `city_id > 0`.
        -   **Luồng Đặc biệt**: Các bản ghi có `city_id = 0` và có tọa độ GPS hợp lệ trong phạm vi Việt Nam.

## Giai đoạn 2: Workflow Xử lý Thông thường (`city_id > 0`)

Đây là quy trình chuẩn cho các bản ghi có thông tin tỉnh/thành phố ban đầu.

1.  **Xử lý theo Batch**: Dữ liệu được xử lý theo từng lô (mặc định 1000 bản ghi/lô) để quản lý bộ nhớ.
2.  **Mapping Tỉnh (Province Mapping)**:
    -   `city_id` của mỗi bản ghi được đối chiếu với bảng `__province` để tìm ra `province_code` (mã tỉnh) tương ứng.
    -   Nếu không tìm thấy, bản ghi sẽ được ghi nhận lỗi và bỏ qua.
3.  **Lọc Geometry (Filtered Geometry Search)**:
    -   Để tối ưu hiệu suất, tool chỉ tìm kiếm trong không gian geometry của các xã/phường thuộc tỉnh đã được xác định ở bước trên.
4.  **Geometry Matching (Point-in-Polygon)**:
    -   Tọa độ `(latitude, longitude)` của brand office được chuyển thành một điểm (Shapely Point).
    -   Tool sẽ tìm xã/phường (ward) mà điểm này nằm trong polygon của nó.
    -   Đây là bước cốt lõi để xác định chính xác vị trí.

## Giai đoạn 3: Workflow Xử lý Đặc biệt (`city_id = 0`)

Quy trình này được thiết kế để "cứu" các bản ghi không có thông tin tỉnh/thành phố nhưng có tọa độ GPS tốt.

1.  **Không lọc theo Tỉnh**: Bỏ qua bước mapping và lọc tỉnh.
2.  **Tìm kiếm Toàn bộ (Full Geometry Search)**:
    -   Tool sẽ tìm kiếm trên **toàn bộ 3,321 xã/phường** có trong bảng `geo_ward`.
    -   Quy trình này tốn nhiều tài nguyên hơn nhưng tăng tối đa khả năng tìm thấy kết quả.

## Giai đoạn 4: Matching và Chuẩn hóa (Áp dụng cho cả 2 luồng)

Sau khi đã có được một danh sách các xã/phường tiềm năng (đã được lọc hoặc không), tool sẽ áp dụng chiến lược matching 3 cấp để tìm ra kết quả tốt nhất.

### Chiến lược Matching 3 Cấp (3-Tier Matching Strategy)

Tool sẽ thử lần lượt các phương pháp theo thứ tự ưu tiên từ cao đến thấp:

1.  **Cấp 1: `contains` (Chứa hoàn toàn)**
    -   **Mô tả**: Điểm tọa độ nằm hoàn toàn bên trong polygon của xã/phường.
    -   **Độ chính xác**: Cao nhất. Đây là trường hợp lý tưởng.

2.  **Cấp 2: `intersects` (Giao nhau)**
    -   **Mô tả**: Điểm tọa độ nằm ngay trên đường biên của polygon.
    -   **Độ chính xác**: Trung bình. Hữu ích cho các trường hợp tọa độ nằm ở ranh giới.

3.  **Cấp 3: `buffer` (Vùng đệm)**
    -   **Mô tả**: Tạo một vùng đệm 100m xung quanh polygon và kiểm tra xem điểm có nằm trong vùng đệm đó không.
    -   **Độ chính xác**: Thấp nhất. Dùng để xử lý các sai số nhỏ của GPS, khi điểm nằm rất gần nhưng ngoài ranh giới một chút.

### Chuẩn hóa Địa chỉ bằng Gemini AI

Sau khi xác định được `ward_title` và `province_title` mới, tool sẽ gửi thông tin này cùng với địa chỉ cũ (`address_old`) đến Gemini AI.

-   **Nhiệm vụ của AI**: Dựa trên các quy tắc và ví dụ đã được "dạy" trước, AI sẽ:
    1.  Giữ lại các thông tin chi tiết như số nhà, tên đường, thôn, xóm.
    2.  Loại bỏ hoàn toàn thông tin quận/huyện cũ.
    3.  Gắn thông tin xã/phường và tỉnh/thành phố mới vào để tạo thành một địa chỉ hoàn chỉnh.
-   **Kết quả**: Một chuỗi địa chỉ mới, sạch sẽ và đúng định dạng.

## Giai đoạn 5: Lưu kết quả

1.  **Tổng hợp**: Tất cả các kết quả (bao gồm cả `matched` và `unmatched`) được tổng hợp lại.
2.  **Ghi ra CSV**: Dữ liệu được ghi ra các file CSV tương ứng trong thư mục `brand_office/exports/`.
3.  **Logging**: Toàn bộ quá trình, bao gồm các lỗi, cảnh báo, và thống kê, đều được ghi lại trong file `update_address.log` để tiện cho việc kiểm tra và gỡ lỗi.
