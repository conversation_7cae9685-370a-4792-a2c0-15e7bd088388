# Phân tích <PERSON>ỹ thuật Chu<PERSON>ên sâu

Tài liệu này đi sâu vào kiến trúc phần mềm, mô tả chi tiết các class, ph<PERSON><PERSON><PERSON> thức chính và các quyết định thiết kế quan trọng của công cụ.

## S<PERSON> đồ Kiến trúc và Tương tác Class

```mermaid
classDiagram
    class BrandOfficeToolkit {
        +run()
        +show_menu()
        +run_address_update()
    }

    class BrandOfficeAddressUpdater {
        +run() async
        +process_batch() async
        +process_city_id_zero_records() async
        +find_ward_by_lat_lng()
        #Database Methods
        #File I/O Methods
    }

    class Gemini {
        +convert_address_async() async
        +process_batch_with_rate_limit() async
        +start_chat()
    }
    
    class MemoryOptimizedGemini {
        +process_large_dataset() async
        +process_stream() async
        -check_memory_limit()
    }

    class RateLimiter {
        +wait_if_needed() async
    }

    BrandOfficeToolkit ..> BrandOfficeAddressUpdater : uses
    BrandOfficeAddressUpdater ..> Gemini : uses (global instance)
    BrandOfficeAddressUpdater ..> MemoryOptimizedGemini : uses (optional)
    MemoryOptimizedGemini ..> Gemini : creates (separate instance)
    Gemini ..> RateLimiter : uses
```

--- 

## 1. `BrandOfficeToolkit` - Lớp Giao diện Người dùng

-   **File**: `brand_office/brand_office_toolkit.py`
-   **Mục đích**: Cung cấp một giao diện dòng lệnh (CLI) tương tác, giúp người dùng dễ dàng vận hành công cụ mà không cần biết về các chi tiết kỹ thuật bên trong.
-   **Phương thức chính**:
    -   `run()`: Vòng lặp chính hiển thị menu và nhận lựa chọn của người dùng.
    -   `show_menu()`: In ra các tùy chọn có sẵn.
    -   `run_address_update(use_memory_optimization)`: Khởi tạo và chạy `BrandOfficeAddressUpdater`. Đây là cầu nối quan trọng giữa giao diện và lõi xử lý. Nó sử dụng `asyncio.run()` để thực thi các tác vụ bất đồng bộ.
    -   `check_requirements()`: Tự động kiểm tra và cài đặt các thư viện cần thiết.

## 2. `BrandOfficeAddressUpdater` - Lớp Điều phối Xử lý Chính

-   **File**: `brand_office/update_brand_office_address.py`
-   **Mục đích**: Là "bộ não" của toàn bộ quy trình, chịu trách nhiệm điều phối việc lấy dữ liệu, xử lý geometry, tích hợp AI và lưu kết quả.
-   **Phương thức chính**:
    -   `run() async`: Phương thức chính, thực thi toàn bộ workflow. Nó được thiết kế `async` để hỗ trợ các hoạt động bất đồng bộ.
        -   Tự động gọi `check_dataset_size_and_optimize()` để quyết định có bật chế độ tối ưu bộ nhớ hay không.
        -   Điều phối việc xử lý `city_id > 0` và `city_id = 0`.
    -   `get_..._data()`: Các phương thức chịu trách nhiệm truy vấn và lấy dữ liệu từ các bảng trong database (`brand_office`, `geo_ward`, `__province`).
    -   `find_ward_by_lat_lng(lat, lng, geo_ward_data)`: Lõi của thuật toán geometry. Nhận vào tọa độ và danh sách các ward, trả về `(ward, match_type)` phù hợp nhất dựa trên chiến lược 3 cấp (contains, intersects, buffer).
    -   `process_batch(...) async`: Xử lý một lô dữ liệu thông thường. Nó tạo ra các tác vụ AI và thực thi chúng đồng thời bằng `asyncio.gather()`.
    -   `process_city_id_zero_records(...) async`: Xử lý các bản ghi `city_id=0`. Tương tự `process_batch` nhưng áp dụng logic tìm kiếm trên toàn bộ geometry.
    -   `_process_record_async(...) async`: Một phương thức private để xử lý một bản ghi duy nhất với Gemini AI.
    -   `save_..._to_csv()`: Chịu trách nhiệm lưu kết quả xử lý ra file CSV.

## 3. `Gemini` & `RateLimiter` - Lớp Tích hợp AI

-   **File**: `brand_office/gemini.py`
-   **Mục đích**: Đóng gói tất cả các tương tác với Google Gemini API, bao gồm cả việc quản lý giới hạn request (rate limiting).
-   **Class `Gemini`**:
    -   `__init__(...)`: Khởi tạo model, `RateLimiter`, và `Semaphore` để quản lý các request đồng thời.
    -   `start_chat()`: Gửi đi "prompt hệ thống" ban đầu để "dạy" cho AI về nhiệm vụ cần làm. Trả về `initial_prompt` để tái sử dụng.
    -   `convert_address_async(...) async`: Gửi một yêu cầu chuyển đổi địa chỉ duy nhất. Được quản lý bởi `Semaphore` và `RateLimiter`.
    -   `process_batch_with_rate_limit(...) async`: Xử lý một lô dữ liệu với AI, tạo và thực thi các `tasks` đồng thời.
-   **Class `RateLimiter`**:
    -   `wait_if_needed() async`: Thuật toán cốt lõi. Sử dụng một `deque` (hàng đợi hai đầu) để theo dõi thời gian của các request trong một cửa sổ trượt (sliding window). Nếu số request vượt ngưỡng, nó sẽ `await asyncio.sleep()` một khoảng thời gian phù hợp.

## 4. `MemoryOptimizedGemini` - Lớp Xử lý Dữ liệu Lớn

-   **File**: `brand_office/gemini_optimized.py`
-   **Mục đích**: Cung cấp một giải pháp thay thế để xử lý các bộ dữ liệu cực lớn mà không gây tràn bộ nhớ.
-   **Thiết kế chính**: **Separate Instance**
    -   Khi được khởi tạo, `MemoryOptimizedGemini` sẽ tạo một **instance `Gemini` hoàn toàn mới và riêng biệt**. Điều này ngăn chặn xung đột về chat session, rate limiter và trạng thái với instance `Gemini` toàn cục đang được sử dụng bởi `BrandOfficeAddressUpdater`.
-   **Phương thức chính**:
    -   `process_large_dataset(...) async`: Phương thức chính để xử lý. Nó nhận vào toàn bộ dữ liệu và khởi tạo một `data_generator`.
    -   `data_generator() async`: Đây là một trình tạo (generator) bất đồng bộ. Thay vì xử lý toàn bộ dữ liệu cùng lúc, nó `yield` từng bản ghi đã được xử lý geometry. Điều này giúp dữ liệu được xử lý theo kiểu "dòng chảy" (stream).
    -   `process_stream(...) async`: Nhận dữ liệu từ `data_generator`, nhóm chúng thành các `chunk` nhỏ, và xử lý từng chunk.
    -   `check_memory_limit()`: Sử dụng `psutil` để theo dõi việc sử dụng bộ nhớ. Nếu vượt ngưỡng, nó sẽ cảnh báo và kích hoạt cơ chế thu gom rác (`gc.collect()`) của Python.
