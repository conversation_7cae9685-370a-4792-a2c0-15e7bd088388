import google.generativeai as genai
import logging
import asyncio
import time
from datetime import datetime, timedelta
from collections import deque
from dotenv import load_dotenv
import os
load_dotenv()

genai.configure(api_key=os.getenv('GEMINI_API_KEY'))    

# Thi<PERSON><PERSON> lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exports/brand_office_gemini.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RateLimiter:
    """Optimized rate limiter cho Gemini API - 2000 requests per minute"""

    def __init__(self, max_requests=2000, time_window=60, burst_size=50):
        self.max_requests = max_requests
        self.time_window = time_window  # seconds
        self.burst_size = burst_size  # Allow burst of requests
        self.requests = deque()
        self.last_cleanup = time.time()
        self.cleanup_interval = 5  # Cleanup every 5 seconds

    def _cleanup_old_requests(self, now):
        """Optimized cleanup - only run periodically"""
        if now - self.last_cleanup < self.cleanup_interval:
            return

        cutoff_time = now - self.time_window
        while self.requests and self.requests[0] <= cutoff_time:
            self.requests.popleft()
        self.last_cleanup = now

    async def wait_if_needed(self):
        """Optimized waiting với burst support"""
        now = time.time()
        self._cleanup_old_requests(now)

        # Allow burst if we're well under the limit
        current_count = len(self.requests)
        if current_count < self.max_requests - self.burst_size:
            self.requests.append(now)
            return

        # Check if we need to wait
        if current_count >= self.max_requests:
            sleep_time = self.requests[0] + self.time_window - now
            if sleep_time > 0:
                # Use exponential backoff for better distribution
                actual_sleep = min(sleep_time, 1.0)  # Max 1 second wait
                logger.info(f"⏳ Rate limit reached, waiting {actual_sleep:.2f}s...")
                await asyncio.sleep(actual_sleep)
                return await self.wait_if_needed()

        # Record new request
        self.requests.append(now)

class Gemini:
    def __init__(self, model='gemini-2.0-flash-001', rate_limit_rpm=2000, max_concurrent=20):
        self.model = genai.GenerativeModel(model)
        self.chat = self.model.start_chat(history=[])
        self.rate_limiter = RateLimiter(max_requests=rate_limit_rpm, time_window=60)
        self.request_count = 0
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.session_initialized = False

    def build_conversion_prompt(self):
        """Xây dựng một prompt chi tiết cho Gemini để đảm bảo kết quả chính xác.
    Đây là "bộ não" của tác vụ.
        """
        # Sử dụng kỹ thuật "few-shot prompting" bằng cách đưa ra vài ví dụ
        # để AI hiểu rõ định dạng và yêu cầu.
        prompt = f"""
Bạn là một chuyên gia xử lý và chuẩn hóa địa chỉ tại Việt Nam.
Nhiệm vụ của bạn là nhận một địa chỉ cũ và thông tin xã/tỉnh mới, sau đó tạo ra một địa chỉ mới theo các quy tắc nghiêm ngặt sau:

1.  **Giữ nguyên** các chi tiết của địa chỉ cũ như: số nhà, tên đường, ngõ, ngách, hẻm, thôn, xóm, ấp.
2.  **Xóa bỏ hoàn toàn** thông tin về Quận/Huyện/Thị xã cũ.
3.  **Thay thế** hoặc **bổ sung** thông tin Xã/Phường bằng "Xã/Phường mới" được cung cấp.
4.  **Thay thế** hoặc **bổ sung** thông tin Tỉnh/Thành phố bằng "Tỉnh/Thành phố mới" được cung cấp.
5.  **Chỉ trả về chuỗi địa chỉ mới**, không thêm bất kỳ lời giải thích hay câu chữ thừa nào.

---
**Ví dụ 1:**
- Địa chỉ cũ: Thôn Đoài, xã Minh Trí, huyện Sóc Sơn, Thành phố Hà Nội
- Xã/Phường mới: Xã Tân Minh
- Tỉnh/Thành phố mới: Thành phố Hà Nội
- KẾT QUẢ ĐÚNG: Thôn Đoài, Xã Tân Minh, Thành phố Hà Nội

**Ví dụ 2:**
- Địa chỉ cũ: Số 25, phố Tràng Tiền, quận Hoàn Kiếm, Hà Nội
- Xã/Phường mới: Phường Tràng Tiền
- Tỉnh/Thành phố mới: Thành phố Hà Nội
- KẾT QUẢ ĐÚNG: Số 25, phố Tràng Tiền, Phường Tràng Tiền, Thành phố Hà Nội

**Ví dụ 3:**
- Địa chỉ cũ: Thôn 5, xã Ea M'nang, huyện Cư M'gar, tỉnh Đắk Lắk
- Xã/Phường mới: Xã Dliêya
- Tỉnh/Thành phố mới: Tỉnh Đắk Nông
- KẾT QUẢ ĐÚNG: Thôn 5, Xã Dliêya, Tỉnh Đắk Nông
---

**Sau tin nhắn này, tôi sẽ bắt đầu gửi dữ liệu. Hãy sẵn sàng.**
"""
        return prompt
    def start_chat(self):
        self.chat = self.model.start_chat(history=[])
        self.session_initialized = False # RESET LẠI TRẠNG THÁI SESSION
        # 1. GỬI PROMPT HỆ THỐNG (DÀI) CHỈ MỘT LẦN
        print("Đang gửi hướng dẫn ban đầu cho Gemini...")
        initial_prompt = self.build_conversion_prompt()
        # Chúng ta không cần phản hồi của tin nhắn này, chỉ cần "dạy" cho AI
        return initial_prompt
    
    
    async def convert_address_async(self, initial_prompt, brand_office, ward, province):
        """
        Optimized async function với concurrency control và connection pooling.
        """

        async with self.semaphore:  # Limit concurrent requests
            try:
                # Initialize session once
                if not self.session_initialized:
                    await self.rate_limiter.wait_if_needed()
                    await self.chat.send_message_async(initial_prompt)
                    self.session_initialized = True
                    self.request_count += 1
                    logger.info(f"📤 Session initialized (Request #{self.request_count})")

                # Prepare optimized message
                short_message = self.format_subsequent_message(
                    brand_office['address_old'],
                    ward,
                    province
                )

                # Apply rate limiting
                await self.rate_limiter.wait_if_needed()

                # Send request with timeout
                # Mock data for local testing - return short_message directly
                if os.getenv('MOCK_GEMINI', 'false').lower() == 'true':
                    # Mock response for testing
                    class MockResponse:
                        def __init__(self, text):
                            self.text = text

                    response = MockResponse(brand_office['address_old'])
                else:
                    # Real API call
                    response = await asyncio.wait_for(
                        self.chat.send_message_async(short_message),
                        timeout=30.0  # 30 second timeout
                    )

                self.request_count += 1

                # Optimized logging - only every 50 requests
                if self.request_count % 50 == 0:
                    logger.info(f"📊 Processed {self.request_count} requests")
                return response.text.strip()

            except asyncio.TimeoutError:
                logger.warning(f"⏰ Timeout for record {brand_office['id']}")
                return "TIMEOUT_ERROR"
            except Exception as e:
                logger.error(f"❌ Error processing record {brand_office['id']}: {e}")
                return "CONVERSION_ERROR"
    
    def format_subsequent_message(self, old_address, new_commune, new_province):
        """
        Định dạng tin nhắn ngắn gọn cho mỗi địa chỉ sau tin nhắn đầu tiên.
        """
        return f"""
    - Địa chỉ cũ: "{old_address}"
    - Xã/Phường mới: "{new_commune}"
    - Tỉnh/Thành phố mới: "{new_province}"
    """

    async def process_batch_with_rate_limit(self, brand_office_list, ward_list, province_list):
        """
        Optimized batch processing với concurrent execution
        """
        logger.info(f"🚀 Processing batch: {len(brand_office_list)} records (max concurrent: {self.max_concurrent})")

        initial_prompt = self.build_conversion_prompt()
        start_time = time.time()

        # Create tasks for concurrent execution
        tasks = []
        for brand_office, ward, province in zip(brand_office_list, ward_list, province_list):
            task = self.convert_address_async(initial_prompt, brand_office, ward, province)
            tasks.append((task, brand_office, ward, province))

        # Process tasks concurrently with progress tracking
        results = []
        completed = 0

        # Process in chunks to avoid memory issues
        chunk_size = min(self.max_concurrent * 2, 100)

        for i in range(0, len(tasks), chunk_size):
            chunk_tasks = tasks[i:i + chunk_size]

            # Execute chunk concurrently
            chunk_results = await asyncio.gather(
                *[task[0] for task in chunk_tasks],
                return_exceptions=True
            )

            # Process results
            for result, (_, brand_office, ward, province) in zip(chunk_results, chunk_tasks):
                completed += 1

                if isinstance(result, Exception):
                    logger.error(f"❌ Exception for record {brand_office['id']}: {result}")
                    result = "EXCEPTION_ERROR"

                # Determine status
                status = 'success'
                if result in ['CONVERSION_ERROR', 'TIMEOUT_ERROR', 'EXCEPTION_ERROR', 'PROCESSING_ERROR']:
                    status = 'error'

                results.append({
                    'id': brand_office['id'],
                    'original_address': brand_office['address_old'],
                    'new_address': result,
                    'ward': ward,
                    'province': province,
                    'status': status
                })

                # Progress logging
                if completed % 25 == 0:
                    elapsed = time.time() - start_time
                    rate = completed / elapsed * 60
                    eta = (len(brand_office_list) - completed) / (completed / elapsed) if completed > 0 else 0
                    logger.info(f"📊 Progress: {completed}/{len(brand_office_list)} | Rate: {rate:.1f} RPM | ETA: {eta:.1f}s")

        total_time = time.time() - start_time
        avg_rate = len(brand_office_list) / total_time * 60
        success_count = len([r for r in results if r['status'] == 'success'])

        logger.info(f"✅ Batch completed: {len(results)} records in {total_time:.1f}s")
        logger.info(f"📈 Avg rate: {avg_rate:.1f} RPM | Success: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")

        return results

    def get_rate_limit_stats(self):
        """Lấy thống kê rate limiting"""
        return {
            'total_requests': self.request_count,
            'current_queue_size': len(self.rate_limiter.requests),
            'max_requests_per_minute': self.rate_limiter.max_requests
        }
  
# Khởi tạo instance gemini
# Chỉ khởi tạo khi được import, không phải khi chạy trực tiếp
if __name__ != "__main__":
    try:
        gemini = Gemini()
        logger.info("✅ Gemini instance đã được khởi tạo thành công")
    except Exception as e:
        logger.error(f"❌ Lỗi khởi tạo Gemini instance: {e}")
        gemini = None
