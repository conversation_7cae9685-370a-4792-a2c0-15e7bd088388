Bạn là 1 lập trình viên có nhiều kinh nghiệm làm việc với python và bản đồ. Nhiệm vụ của bạn là xử lý task như sau:
**Cập nhật địa chỉ brand_office dựa trên geometry data**

**<PERSON><PERSON><PERSON> tiêu:** Sử dụng dữ liệu geometry từ Task 4 để xác định chính xác xã/phường và tỉnh/thành phố cho từng brand_office dựa trên tọa độ lat/long, sau đó cập nhật lại cột address với định dạng mới. 

**<PERSON><PERSON>u cầu cập nhật cột address:**
- **Định dạng hiện tại:** "<PERSON><PERSON> nhà, Tên đườ<PERSON> (Khu dân cư), X<PERSON>/Phường, Quận/Huyện, Tỉnh/Thành phố"
- **Định dạng mới:** "<PERSON><PERSON> nh<PERSON>, <PERSON><PERSON><PERSON> (<PERSON><PERSON>ân <PERSON>), <PERSON><PERSON>/Phường, Tỉnh/Thành phố" (bỏ Quận/Huyện)
- **<PERSON><PERSON><PERSON>i lệ:** Một số địa chỉ sẽ không có đủ các thông tin hoặc không có dấu phẩy phân cách. ví dụ 65 Vạn Bảo, Ba Đình
**Thư mục làm việc:** `brand_office/`

**Các bước thực hiện chi tiết:**

1. **Thiết lập môi trường:**
   - Tạo thư mục `brand_office/` và `brand_office/exports/`
   - Cài đặt thư viện cần thiết: `geopandas`, `shapely`, `pandas`

2. **Truy xuất dữ liệu:**
   - Lấy dữ liệu từ bảng `brand_office` (batch 1000 records/lần): `id, lat, long, city_id, address_old`, bỏ qua các bản ghi address_old is null hoặc empty
   - Lấy toàn bộ dữ liệu bảng `__province`: `id, new_pti_id` với điều kiện `is_new=1`
   - Lấy toàn bộ dữ liệu bảng `geo_ward`: `geometry, geo_province_code, province_title, ward_title, code`

3. **Xử lý mapping và geometry checking:**
   - Với mỗi brand_office record:
     - Map `brand_office.city_id` với `__province.id` (where `__province.is_new=1`) để lấy `new_pti_id`
     - Filter `geo_ward` records theo `geo_province_code = new_pti_id`
     - Sử dụng GeoPandas để kiểm tra point (lat, long) có nằm trong geometry polygon của từng ward không
     - Parse `geometry` field (JSON string format GeoJSON) thành Shapely geometry object

4. **Xử lý kết quả:**
   - Khi tìm được ward chứa tọa độ brand_office:
     - Trích xuất "Số nhà, Tên đường (Khu dân cư)" từ address hiện tại
     - Tạo address mới: "{Số nhà, Tên đường (Khu dân cư)}, {ward_title}, {province_title}"
     - Lưu thông tin vào CSV: `brand_office.*, geo_ward.province_title, geo_ward.ward_title, geo_ward.code, geo_ward.geo_province_code, new_title`

5. **Output:**
   - File CSV trong `brand_office/exports/brand_office.csv` chứa kết quả mapping

**Lưu ý kỹ thuật:**
- Sử dụng `geopandas.points_from_xy()` để tạo Point geometry
- Sử dụng `geometry.contains()` hoặc `geometry.intersects()` để kiểm tra point-in-polygon
- Xử lý exception cho các trường hợp geometry không hợp lệ hoặc không tìm được ward phù hợp
- Log các trường hợp không match được để review manual
- Viết thành toolkit với các lựa chọn để có thể bổ sung thêm tính năng

