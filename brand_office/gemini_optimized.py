#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Memory-optimized Gemini processor cho large datasets
Sử dụng streaming và memory management
"""

import gc
import logging
import asyncio
from typing import AsyncGenerator, List, Dict, Any

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logging.warning("psutil not available, memory monitoring disabled")

logger = logging.getLogger(__name__)

class MemoryOptimizedGemini:
    """Memory-optimized wrapper cho Gemini với streaming processing"""

    def __init__(self, memory_limit_mb=1024):
        # Create SEPARATE Gemini instance để tránh conflicts
        from gemini import Gemini
        self.gemini = Gemini()  # Separate instance
        self.memory_limit_mb = memory_limit_mb
        self.processed_count = 0
        self.memory_warnings = 0

        # Initialize separate chat session
        self.initial_prompt = self.gemini.start_chat()
        logger.info("🧠 Memory-optimized Gemini instance created (separate from main)")
        
    def get_memory_usage(self):
        """Lấy memory usage hiện tại"""
        if PSUTIL_AVAILABLE:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024  # MB
        else:
            # Fallback: return 0 if psutil not available
            return 0
    
    def check_memory_limit(self):
        """Kiểm tra và cảnh báo memory usage"""
        current_memory = self.get_memory_usage()
        if current_memory > self.memory_limit_mb:
            self.memory_warnings += 1
            logger.warning(f"⚠️ Memory usage: {current_memory:.1f}MB (limit: {self.memory_limit_mb}MB)")
            
            # Force garbage collection
            gc.collect()
            
            # Check again after GC
            after_gc = self.get_memory_usage()
            logger.info(f"🧹 After GC: {after_gc:.1f}MB (freed: {current_memory - after_gc:.1f}MB)")
            
            return after_gc > self.memory_limit_mb
        return False
    
    async def process_stream(self, data_stream: AsyncGenerator[Dict[str, Any], None], 
                           chunk_size: int = 100) -> AsyncGenerator[List[Dict[str, Any]], None]:
        """
        Stream processing với memory management
        """
        logger.info(f"🌊 Starting stream processing (chunk_size: {chunk_size})")
        
        chunk = []
        async for item in data_stream:
            chunk.append(item)
            
            # Process chunk when full
            if len(chunk) >= chunk_size:
                # Check memory before processing
                if self.check_memory_limit():
                    logger.warning("💾 Memory limit exceeded, reducing chunk size")
                    chunk_size = max(10, chunk_size // 2)
                
                # Process chunk
                results = await self._process_chunk(chunk)
                yield results

                # Clear chunk and update stats
                chunk.clear()
                self.processed_count += len(results)

                # Log progress
                if self.processed_count % 100 == 0:
                    memory_mb = self.get_memory_usage()
                    logger.info(f"📊 Processed: {self.processed_count} | Memory: {memory_mb:.1f}MB")
        
        # Process remaining items
        if chunk:
            results = await self._process_chunk(chunk)
            yield results
            self.processed_count += len(results)
    
    async def _process_chunk(self, chunk: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process một chunk data với separate Gemini instance"""
        results = []

        try:
            # Process each item in chunk với separate instance
            for item in chunk:
                try:
                    brand_office = item['brand_office']
                    ward = item['ward']
                    province = item['province']
                    ward_code = item['ward_code']
                    geo_province_code = item['geo_province_code']

                    # Sử dụng separate Gemini instance
                    new_address = await self.gemini.convert_address_async(
                        self.initial_prompt, brand_office, ward, province
                    )

                    result = {
                        'id': brand_office.get('id', '-1'),
                        'latitude': brand_office.get('latitude', ''),
                        'longitude': brand_office.get('longitude', ''),
                        'city_id': brand_office.get('city_id', ''),
                        'geo_province_code': geo_province_code,
                        'ward_code': ward_code,
                        'original_address': brand_office.get('address_old', ''),
                        'new_address': new_address,
                        'ward': ward,
                        'province': province,
                        'status': 'success'
                    }

                except Exception as e:
                    logger.warning(f"⚠️ Error processing item {brand_office.get('id', 'unknown')}: {e}")
                    result = {
                        'id': brand_office.get('id', 'unknown'),
                        'latitude': brand_office.get('latitude', ''),
                        'longitude': brand_office.get('longitude', ''),
                        'city_id': brand_office.get('city_id', ''),
                        'geo_province_code': geo_province_code,
                        'ward_code': ward_code,
                        'original_address': brand_office.get('address_old', ''),
                        'new_address': 'ITEM_ERROR',
                        'ward': ward,
                        'province': province,
                        'status': 'error'
                    }

                results.append(result)

            return results

        except Exception as e:
            logger.error(f"❌ Error processing chunk: {e}")
            # Return error results for all items in chunk
            return [
                {
                    'id': item['brand_office'].get('id', 'unknown'),
                    'latitude': item['brand_office'].get('latitude', ''),
                    'longitude': item['brand_office'].get('longitude', ''),
                    'city_id': item['brand_office'].get('city_id', ''),
                    'geo_province_code': item.get('geo_province_code', ''),
                    'ward_code': item.get('ward_code', ''),
                    'original_address': item['brand_office'].get('address_old', ''),
                    'new_address': 'CHUNK_ERROR',
                    'ward': item.get('ward', ''),
                    'province': item.get('province', ''),
                    'status': 'error'
                }
                for item in chunk
            ]
    
    async def process_large_dataset(self, data_stream: AsyncGenerator, geo_ward_data, output_file='exports/large_dataset_results.csv'):
        """
        Xử lý large dataset với streaming và memory optimization
        Input: data_stream (async generator), geo_ward_data (list)
        """
        logger.info("🚀 Starting large dataset processing với separate Gemini instance...")

        import pandas as pd
        from update_brand_office_address import BrandOfficeAddressUpdater # For find_ward_by_lat_lng

        # Create a temporary updater instance to access the find_ward_by_lat_lng method
        # This is a workaround to avoid making find_ward_by_lat_lng a static method
        temp_updater = BrandOfficeAddressUpdater()

        # Initialize CSV file with headers
        headers = ['id', 'latitude', 'longitude', 'city_id', 
                'geo_province_code',  'ward_code', 'province_title', 'ward_title', 'address_old', 'new_address', 'status']
        pd.DataFrame(columns=headers).to_csv(output_file, index=False)

        total_processed = 0
        total_success = 0
        total_errors = 0

        async def processed_stream_generator():
            """Generator to perform geometry matching on the input stream"""
            async for batch in data_stream:
                for record in batch:
                    ward_info = None
                    try:
                        # Check if geo_ward_data is GeoDataFrame or list
                        if hasattr(geo_ward_data, 'empty'):  # GeoDataFrame
                            # Filter GeoDataFrame
                            
                            ward_info, _ = temp_updater.find_ward_by_lat_lng(
                                float(record['latitude']),
                                float(record['longitude']),
                                geo_ward_data
                            )
                        else:  # List  
                            ward_info, _ = temp_updater.find_ward_by_lat_lng_legacy(
                                float(record['latitude']),
                                float(record['longitude']),
                                geo_ward_data
                            )
                        if ward_info and isinstance(ward_info, dict):
                            # Validate required fields
                            required_fields = ['ward_title', 'province_title', 'code', 'geo_province_code']
                            if all(field in ward_info for field in required_fields):
                                yield {
                                    'brand_office': record,
                                    'ward': ward_info['ward_title'],
                                    'province': ward_info['province_title'],
                                    'ward_code': ward_info['code'],
                                    'geo_province_code': ward_info['geo_province_code']
                                }
                            else:
                                logger.warning(f"⚠️ Ward info missing required fields: {ward_info}")
                        else:
                            # No ward found or invalid ward_info
                            pass
                    except Exception as e:
                        logger.error(f"❌ Error in processed_stream_generator for record {record.get('id', 'unknown')}: {e}")
                        continue

        # Process stream in chunks
        async for chunk_results in self.process_stream(processed_stream_generator(), chunk_size=1000):
            # Convert to proper format
            formatted_results = []
            for result in chunk_results:
                try:
                    # Validate result structure
                    if not isinstance(result, dict):
                        logger.error(f"❌ Result is not dict: {type(result)} - {result}")
                        continue

                    # Extract brand_office data safely
                    brand_office = result.get('brand_office', {})
                    if not isinstance(brand_office, dict):
                        logger.error(f"❌ brand_office is not dict: {type(brand_office)}")
                        continue

                    formatted_result = {
                        'id': result.get('id', '-2'),
                        'latitude': result.get('latitude', ''),
                        'longitude': result.get('longitude', ''),
                        'city_id': result.get('city_id', ''),
                        'geo_province_code': result.get('geo_province_code', ''),
                        'ward_code': result.get('ward_code', ''),
                        'province_title': result.get('province', ''),
                        'ward_title': result.get('ward', ''),
                        'address_old': result.get('original_address', ''),
                        'new_address': result.get('new_address', ''),  # This might come from AI processing
                        'status': result.get('status', 'matched')  # Default to matched for geometry matches
                    }
                    formatted_results.append(formatted_result)
                except Exception as format_error:
                    logger.error(f"❌ Error formatting result: {format_error}")
                    logger.error(f"   Result: {result}")
                    continue

            # Append to CSV immediately (streaming write)
            chunk_df = pd.DataFrame(formatted_results)
            chunk_df.to_csv(output_file, mode='a', header=False, index=False)

            # Update stats - safe access
            chunk_success = len([
                r for r in chunk_results
                if isinstance(r, dict) and r.get('status') == 'success'
            ])
            chunk_errors = len(chunk_results) - chunk_success

            total_processed += len(chunk_results)
            total_success += chunk_success
            total_errors += chunk_errors

            # Log chunk stats
            logger.info(f"✅ Chunk completed: {len(chunk_results)} records | "
                       f"Success: {chunk_success} | Errors: {chunk_errors}")

        # Final stats
        success_rate = total_success / total_processed * 100 if total_processed > 0 else 0

        logger.info(f"🎉 Large dataset processing completed!")
        logger.info(f"📊 Final stats:")
        logger.info(f"   - Total processed: {total_processed}")
        logger.info(f"   - Success: {total_success} ({success_rate:.1f}%)")
        logger.info(f"   - Errors: {total_errors}")
        logger.info(f"   - Memory warnings: {self.memory_warnings}")
        logger.info(f"   - Output file: {output_file}")

        return {
            'total_processed': total_processed,
            'total_success': total_success,
            'total_errors': total_errors,
            'success_rate': success_rate,
            'memory_warnings': self.memory_warnings,
            'output_file': output_file
        }